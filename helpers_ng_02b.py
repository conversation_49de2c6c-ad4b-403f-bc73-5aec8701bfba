import numpy as np
import shapely
import shapely.ops
import shapely.vectorized
import scipy.optimize
import scipy.special
import bpy
from mathutils import Vector
import trimesh
import time
from dataclasses import dataclass
from typing import Tuple, Optional, Dict, List, Union
from shapely.ops import nearest_points
from scipy.spatial.distance import cdist
from scipy.optimize import minimize
from scipy.linalg import svd

class EllipseFitter:
    """
    A comprehensive ellipse fitting class that handles various degenerate cases
    and provides multiple fitting methods.
    """

    def __init__(self):
        self.last_error = None
        self.last_method_used = None

    def fit(self, x, y, method='auto'):
        """
        Fit an ellipse to the given points using the specified method.

        Parameters:
        -----------
        x, y : array-like
            Coordinates of the points
        method : str
            'auto' - try multiple methods automatically
            'direct' - Fitzgibbon direct least squares
            'svd' - SVD-based fitting
            'geometric' - Geometric distance minimization

        Returns:
        --------
        dict or None
            Dictionary with ellipse parameters if successful, None if failed
        """
        x = np.asarray(x, dtype=float)
        y = np.asarray(y, dtype=float)

        if len(x) != len(y):
            self.last_error = "x and y must have the same length"
            return None

        if len(x) < 5:
            self.last_error = "At least 5 points are required to fit an ellipse"
            return None

        # Check for degenerate cases
        if self._check_degenerate_cases(x, y):
            return None

        if method == 'auto':
            return self._fit_auto(x, y)
        elif method == 'direct':
            return self._fit_direct(x, y)
        elif method == 'svd':
            return self._fit_svd(x, y)
        elif method == 'geometric':
            return self._fit_geometric(x, y)
        else:
            self.last_error = f"Unknown method: {method}"
            return None

    def _check_degenerate_cases(self, x, y):
        """Check for various degenerate cases"""

        # Check for duplicate points
        unique_points = len(set(zip(x, y)))
        if unique_points < 5:
            self.last_error = f"Only {unique_points} unique points, need at least 5"
            return True

        # Check for collinearity
        if self._is_collinear(x, y):
            self.last_error = "Points are collinear"
            return True

        # Check for points forming a line segment (all points lie on a line)
        if self._points_on_line_segment(x, y):
            self.last_error = "Points lie on a line segment"
            return True

        return False

    def _is_collinear(self, x, y, tolerance=1e-10):
        """Check if points are approximately collinear"""
        if len(x) < 3:
            return True

        # Use cross product to check collinearity
        for i in range(2, len(x)):
            # Vector from point 0 to point 1
            v1 = np.array([x[1] - x[0], y[1] - y[0]])
            # Vector from point 0 to point i
            v2 = np.array([x[i] - x[0], y[i] - y[0]])
            # Cross product magnitude
            cross_product = abs(v1[0] * v2[1] - v1[1] * v2[0])
            if cross_product > tolerance:
                return False
        return True

    def _points_on_line_segment(self, x, y, tolerance=1e-8):
        """Check if all points lie approximately on a line segment"""
        if len(x) < 3:
            return True

        # Fit a line to the points and check residuals
        A = np.vstack([x, np.ones(len(x))]).T
        try:
            m, b = np.linalg.lstsq(A, y, rcond=None)[0]
            residuals = np.abs(y - (m * x + b))
            return np.all(residuals < tolerance)
        except:
            return False

    def _fit_auto(self, x, y):
        """Try multiple methods automatically"""
        methods = ['direct', 'svd', 'geometric']

        for method in methods:
            result = getattr(self, f'_fit_{method}')(x, y)
            if result is not None:
                self.last_method_used = method
                return result

        self.last_error = "All fitting methods failed"
        return None

    def _fit_direct(self, x, y):
        """Direct least squares ellipse fitting (Fitzgibbon method)"""
        # Implementation temporarily commented out
        # try:
        #     D1 = np.vstack([x**2, x*y, y**2]).T
        #     D2 = np.vstack([x, y, np.ones(len(x))]).T
        #     S1 = D1.T @ D1
        #     S2 = D1.T @ D2
        #     S3 = D2.T @ D2

        #     # Check if S3 is invertible
        #     if np.linalg.det(S3) < 1e-12:
        #         self.last_error = "S3 matrix is singular (direct method)"
        #         return None

        #     T = -np.linalg.inv(S3) @ S2.T
        #     M = S1 + S2 @ T
        #     C = np.array(((0, 0, 2), (0, -1, 0), (2, 0, 0)), dtype=float)
        #     M = np.linalg.inv(C) @ M

        #     eigval, eigvec = np.linalg.eig(M)

        #     # Find eigenvectors that satisfy the ellipse constraint
        #     con = 4 * eigvec[0] * eigvec[2] - eigvec[1]**2
        #     valid_indices = np.where(con > 0)[0]

        #     if len(valid_indices) == 0:
        #         self.last_error = "No valid ellipse solution found (direct method)"
        #         return None

        #     # Choose the eigenvector with the smallest positive eigenvalue
        #     valid_eigenvalues = eigval[valid_indices]
        #     min_idx = valid_indices[np.argmin(valid_eigenvalues)]
        #     ak = eigvec[:, min_idx]

        #     coeffs = np.concatenate((ak, T @ ak)).ravel()
        #     return self._coeffs_to_params(coeffs)

        # except Exception as e:
        #     self.last_error = f"Direct method failed: {str(e)}"
        #     return None

        self.last_error = "Direct method is temporarily disabled"
        return None

    def _fit_svd(self, x, y):
        """SVD-based ellipse fitting with cost/quality metric"""
        try:
            # Design matrix for general conic: ax^2 + bxy + cy^2 + dx + ey + f = 0
            D = np.column_stack([x**2, x*y, y**2, x, y, np.ones(len(x))])

            # SVD decomposition
            U, s, Vt = svd(D)

            # The solution is the last column of V (or last row of Vt)
            coeffs = Vt[-1, :]

            # Check if this represents an ellipse (4ac - b^2 > 0)
            a, b, c = coeffs[0], coeffs[1], coeffs[2]
            discriminant = 4*a*c - b**2

            if discriminant <= 0:
                self.last_error = "SVD solution does not represent an ellipse"
                return None

            # Calculate cost metric based on residual sum of squares
            # For each point, calculate how well it satisfies the ellipse equation
            residuals = (a * x**2 + b * x * y + c * y**2 +
                        coeffs[3] * x + coeffs[4] * y + coeffs[5])

            # Normalize residuals by the ellipse equation coefficients to get geometric meaning
            # Use the Frobenius norm of the coefficient vector for normalization
            coeff_norm = np.linalg.norm(coeffs)
            if coeff_norm > 1e-12:
                normalized_residuals = residuals / coeff_norm
            else:
                normalized_residuals = residuals

            # Cost is the root mean square of normalized residuals (lower is better)
            cost = np.sqrt(np.mean(normalized_residuals**2))

            # Get ellipse parameters
            ellipse_params = self._coeffs_to_params(coeffs)

            if ellipse_params is not None:
                # Add cost to the returned parameters
                ellipse_params['cost'] = cost
                return ellipse_params
            else:
                return None

        except Exception as e:
            self.last_error = f"SVD method failed: {str(e)}"
            return None

    def _fit_geometric(self, x, y):
        """Geometric ellipse fitting using optimization"""
        # Implementation temporarily commented out
        # try:
        #     # Initial guess
        #     x_center = np.mean(x)
        #     y_center = np.mean(y)
        #     a_init = np.std(x) * 2
        #     b_init = np.std(y) * 2
        #     theta_init = 0

        #     initial_params = [x_center, y_center, a_init, b_init, theta_init]

        #     def objective(params):
        #         x0, y0, a, b, theta = params
        #         if a <= 0 or b <= 0:
        #             return 1e10

        #         # Calculate geometric distance from each point to the ellipse
        #         total_distance = 0
        #         for xi, yi in zip(x, y):
        #             # Transform point to ellipse coordinate system
        #             cos_theta = np.cos(theta)
        #             sin_theta = np.sin(theta)
        #             dx = xi - x0
        #             dy = yi - y0
        #             x_rot = dx * cos_theta + dy * sin_theta
        #             y_rot = -dx * sin_theta + dy * cos_theta

        #             # Find closest point on ellipse (approximate)
        #             if abs(x_rot) < 1e-10 and abs(y_rot) < 1e-10:
        #                 distance = 0
        #             else:
        #                 t = np.arctan2(y_rot * a, x_rot * b)
        #                 x_ellipse = a * np.cos(t)
        #                 y_ellipse = b * np.sin(t)

        #                 # Transform back
        #                 x_closest = x0 + x_ellipse * cos_theta - y_ellipse * sin_theta
        #                 y_closest = y0 + x_ellipse * sin_theta + y_ellipse * cos_theta

        #                 distance = np.sqrt((xi - x_closest)**2 + (yi - y_closest)**2)

        #             total_distance += distance**2

        #         return total_distance

        #     # Bounds to ensure positive semi-axes
        #     bounds = [(-np.inf, np.inf), (-np.inf, np.inf), (0.01, np.inf), (0.01, np.inf), (-np.pi, np.pi)]

        #     result = minimize(objective, initial_params, bounds=bounds, method='L-BFGS-B')

        #     if result.success:
        #         x0, y0, a, b, theta = result.x
        #         # Ensure a >= b (semi-major >= semi-minor)
        #         if b > a:
        #             a, b = b, a
        #             theta += np.pi/2

        #         e = np.sqrt(1 - (b/a)**2) if a > b else 0

        #         return {
        #             'center': (x0, y0),
        #             'semi_major': a,
        #             'semi_minor': b,
        #             'eccentricity': e,
        #             'rotation': theta % np.pi,
        #             'method': 'geometric'
        #         }
        #     else:
        #         self.last_error = "Geometric optimization failed to converge"
        #         return None

        # except Exception as e:
        #     self.last_error = f"Geometric method failed: {str(e)}"
        #     return None

        self.last_error = "Geometric method is temporarily disabled"
        return None

    def _coeffs_to_params(self, coeffs):
        """Convert conic coefficients to ellipse parameters"""
        try:
            if len(coeffs) != 6:
                return None

            a, b, c, d, e, f = coeffs

            # Check if it's an ellipse
            discriminant = b**2 - 4*a*c
            if discriminant >= 0:
                self.last_error = "Coefficients do not represent an ellipse"
                return None

            # Calculate center
            den = b**2 - 4*a*c
            x0 = (2*c*d - b*e) / den
            y0 = (2*a*e - b*d) / den

            # Calculate semi-axes and rotation
            # This uses the standard formulas for conic section parameters
            theta = 0.5 * np.arctan2(b, a - c) if abs(b) > 1e-10 else 0

            # Calculate the semi-axes lengths
            cos_theta = np.cos(theta)
            sin_theta = np.sin(theta)

            # Transform to canonical form
            A = a * cos_theta**2 + b * cos_theta * sin_theta + c * sin_theta**2
            C = a * sin_theta**2 - b * cos_theta * sin_theta + c * cos_theta**2

            # Calculate the constant term in canonical form
            F = a*x0**2 + b*x0*y0 + c*y0**2 + d*x0 + e*y0 + f

            if F * A > 0 or F * C > 0:
                self.last_error = "Invalid ellipse parameters (negative semi-axes)"
                return None

            semi_major = np.sqrt(-F / A)
            semi_minor = np.sqrt(-F / C)

            # Ensure semi_major >= semi_minor
            if semi_minor > semi_major:
                semi_major, semi_minor = semi_minor, semi_major
                theta += np.pi/2

            eccentricity = np.sqrt(1 - (semi_minor/semi_major)**2) if semi_major > semi_minor else 0

            return {
                'center': (x0, y0),
                'semi_major': semi_major,
                'semi_minor': semi_minor,
                'eccentricity': eccentricity,
                'rotation': theta % np.pi,
                'coefficients': coeffs,
                'method': 'algebraic'
            }

        except Exception as e:
            self.last_error = f"Parameter conversion failed: {str(e)}"
            return None

    def get_ellipse_points(self, params, npts=100):
        """Generate points on the fitted ellipse"""
        if params is None:
            return None, None

        x0, y0 = params['center']
        a = params['semi_major']
        b = params['semi_minor']
        theta = params['rotation']

        t = np.linspace(0, 2*np.pi, npts)
        x = x0 + a * np.cos(t) * np.cos(theta) - b * np.sin(t) * np.sin(theta)
        y = y0 + a * np.cos(t) * np.sin(theta) + b * np.sin(t) * np.cos(theta)

        return x, y


def create_ellipse(anchor_point, semi_major_axis, sampling_start_angle=0.0,
                   resolution=71, semi_minor_axis=None, rotation=0.0, anchor_on_perimeter=False, only_center=False):
    """Generates the points and center of a circle or ellipse.

    NOTE: This version orients the major axis along the global Y-axis and the
    minor axis along the global X-axis before any rotation is applied.

    Args:
        anchor_point (tuple): A tuple (x, y) that serves as the reference point for the ellipse.
            Its meaning is determined by the 'anchor_on_perimeter' parameter.
        semi_major_axis (float): The semi-major axis (the "main" radius) of the ellipse.
        sampling_start_angle (float, optional): The angle in radians where the point
            generation begins on the ellipse's path. Defaults to 0.0.
        resolution (int, optional): The number of points to generate. Defaults to 72.
        semi_minor_axis (float, optional): The semi-minor axis. If None, it defaults
            to the semi_major_axis, creating a circle. Defaults to None.
        rotation (float, optional): The geometric rotation of the ellipse in radians.
            Defaults to 0.0.
        anchor_on_perimeter (bool, optional):
            - If False (default): 'anchor_point' is the center of the ellipse.
            - If True: 'anchor_point' is the '3 o'clock' point on the
              ellipse's perimeter. Defaults to False.

    Returns:
        tuple: A tuple containing:
            - np.ndarray: An array of [x, y] points for the ellipse.
            - tuple: The (x, y) coordinates of the final calculated center.
    """
    # If semi_minor_axis is not provided, create a circle by making it equal to the semi_major_axis
    local_semi_minor = semi_minor_axis if semi_minor_axis is not None else semi_major_axis

    # --- 1. Calculate the true center of the ellipse based on the anchor ---
    if anchor_on_perimeter:
        # The anchor is the '3 o'clock' point. The vector from the center to this
        # point on our unrotated ellipse is now (semi_minor_axis, 0). We rotate this
        # vector to find the offset from the true center to the anchor point.
        # --- CHANGED ---: Use local_semi_minor instead of semi_major_axis for the offset.
        offset_x = local_semi_minor * np.cos(rotation)
        offset_y = local_semi_minor * np.sin(rotation)

        # The true center is the anchor_point minus this rotated offset vector.
        center_x = anchor_point[0] - offset_x
        center_y = anchor_point[1] - offset_y
    else:
        # The anchor point is the center.
        center_x, center_y = anchor_point

    final_center = (center_x, center_y)
    if only_center:
        return final_center

    # --- 2. Generate points for a base ellipse centered at the origin (0,0) ---
    theta = np.linspace(sampling_start_angle, sampling_start_angle + 2 * np.pi, resolution)
    # --- CHANGED ---: Swapped axes to orient the major axis along Y and minor along X.
    x_base = local_semi_minor * np.cos(theta)  # Minor axis on X
    y_base = semi_major_axis * np.sin(theta)   # Major axis on Y

    # --- 3. Apply rotation to the base points ---
    cos_rot, sin_rot = np.cos(rotation), np.sin(rotation)
    x_rotated = x_base * cos_rot - y_base * sin_rot
    y_rotated = x_base * sin_rot + y_base * cos_rot

    # --- 4. Translate the rotated points to the final center ---
    points = np.column_stack([
        final_center[0] + x_rotated,
        final_center[1] + y_rotated
    ])

    return points, final_center


def find_farthest_outside_point_ng(inner_ellipse_coords: np.ndarray, outer_ellipse: shapely.geometry.Polygon):
    """
    Finds all points on the boundary of ellipse_a that are outside of ellipse_b
    and determines which of these points is farthest from ellipse_b's boundary.

    Args:
        inner_ellipse_coords (numpy array): The boundary coordinates of the inner ellipse.
        outer_ellipse (Shapely Polygon): The outer ellipse.

    Returns:
        float: The maximum distance found. 0 if no points are outside.
    """
    x = inner_ellipse_coords[:, 0]
    y = inner_ellipse_coords[:, 1]

    # Filter points outside ellipse B
    is_inside_b = shapely.vectorized.contains(outer_ellipse, x, y)
    outside_mask = ~is_inside_b

    outside_points_geom = shapely.points(inner_ellipse_coords[outside_mask])

    # Calculate distances
    distances = shapely.distance(outer_ellipse, outside_points_geom)

    # Check if there are any outside points
    if len(distances) == 0:
        return 0.0

    # Find maximum
    return np.max(distances)


def find_t_for_arc_length_fraction(a, b, fraction):
    e_sq = 1.0 - b**2 / a**2
    circumference = 4.0 * a * scipy.special.ellipe(e_sq)
    target_arc_length = fraction * circumference

    def objective_func(t):
        return a * scipy.special.ellipeinc(t, e_sq) - target_arc_length

    try:
        t_solution = scipy.optimize.brentq(objective_func, 0, 2 * np.pi)
    except ValueError:
        if np.isclose(target_arc_length, 0): t_solution = 0.0
        elif np.isclose(target_arc_length, circumference): t_solution = 2 * np.pi
        else: raise ValueError("Could not find a solution for 't'.")
    return t_solution


def rotate_vector(vector, angle_radians):
    """
    Rotates a 2D vector (as a NumPy array) by a given angle around the origin (0,0).

    Args:
        vector (np.ndarray): A 1D NumPy array representing the vector [x, y].
        angle_degrees (float): The rotation angle in degrees (counter-clockwise).

    Returns:
        np.ndarray: A 1D NumPy array representing the new vector [x_rotated, y_rotated].
    """
    c, s = np.cos(angle_radians), np.sin(angle_radians)
    rotation_matrix = np.array([[c, -s],
                                [s, c]])
    return np.dot(rotation_matrix, vector)


def get_point_and_rotation_on_ellipse(outer_ellipse_data, fraction, normalize_normal=True):
    """
    Calculates point and its outward-pointing normal vector on an ellipse.

    Args:
        a (float): Semi-major axis.
        b (float): Semi-minor axis.
        fraction (float): Fraction of the total arc length (0.0 to 1.0).
        start_angle_rad (float): The parametric angle of the start point (fraction=0).
                                 0.0 for 3 o'clock (default).
                                 np.pi/2 for 12 o'clock.
                                 np.pi for 9 o'clock.
        normalize_normal (bool): If True, returns a unit normal vector.

    Returns:
        tuple: A tuple containing (point_vector, normal_vector).
    """
    # 1. Find the parameter 't' relative to the standard 3 o'clock start
    # This step remains the same as it correctly finds the parametric angle
    # for a given arc length fraction.
    outer_semi_major_axis = outer_ellipse_data['minor']
    outer_semi_minor_axis = outer_ellipse_data['major']
    outer_rotation = outer_ellipse_data['rotation']
    outer_center = outer_ellipse_data['center']

    t_arc = find_t_for_arc_length_fraction(outer_semi_major_axis, outer_semi_minor_axis, fraction)

    # 2. Apply the angular offset to get the effective parameter
    # t_effective = t_arc + start_angle_rad

    # 3. Calculate the point vector using the standard parametric equation
    point_vector = np.array([outer_semi_major_axis * np.cos(t_arc), outer_semi_minor_axis * np.sin(t_arc)])
    point_vector = rotate_vector(point_vector, outer_rotation)
    point_vector += outer_center

    # 4. Calculate the outward-pointing normal vector.
    # The tangent is T = [-a*sin(t), b*cos(t)].
    # The outward normal is a 90-degree clockwise rotation of the tangent: N = [b*cos(t), a*sin(t)].
    normal_vector = np.array([outer_semi_minor_axis * np.cos(t_arc), outer_semi_major_axis * np.sin(t_arc)])
    normal_vector = rotate_vector(normal_vector, outer_rotation)
    normal_angle = np.arctan2(normal_vector[1], normal_vector[0])

    if normalize_normal:
        norm = np.linalg.norm(normal_vector)
        if norm > 1e-9: # Check for zero norm to avoid division by zero
            normal_vector /= norm
        else:
            # This case is unlikely for an ellipse but good practice
            normal_vector = np.array([0., 0.])

    return point_vector, normal_angle


def get_ellipse_distances_ng_vectorized(
    inner_ellipses: np.ndarray,
    sides_data: dict
) -> np.ndarray:
    """
    Calculates distances from an array of ellipses to a set of side geometries.

    This is a vectorized version that performs the following steps:
    1. Calculates distances between all ellipses and all sides.
    2. Checks for intersections between all ellipses and all sides.
    3. For intersecting pairs, it calculates the penetration distance relative to the outer polygon.
    4. Uses np.where to combine these results into a final distance matrix.

    Args:
        inner_ellipses (np.ndarray): A NumPy array of shapely.geometry.LineString objects.
        sides_data (dict): A dictionary containing:
            - 'lines' (list or np.ndarray): Side geometries to measure distances to.
            - 'polygon' (shapely.geometry.Polygon): The outer polygon used for penetration depth.

    Returns:
        np.ndarray: A 2D array of shape (num_ellipses, num_sides) containing the distances.
                    Distances are negative if the ellipse intersects the corresponding side line.
    """
    # Ensure inputs are NumPy arrays for broadcasting
    side_lines = np.array(sides_data['lines'])
    outer_polygon = sides_data['polygon']

    if inner_ellipses.size == 0 or side_lines.size == 0:
        return np.empty((len(inner_ellipses), len(side_lines)))

    # To compare every ellipse with every side line, we need to use broadcasting.
    # Reshape inner_ellipses to a column vector (N, 1) to operate against the
    # row vector of side_lines (M,). NumPy/Shapely will broadcast this to (N, M).
    ellipses_col = inner_ellipses[:, np.newaxis]

    # 1. Calculate the intersection mask for all (ellipse, side) pairs
    # The result is a boolean matrix of shape (num_ellipses, num_sides)
    intersects_mask = shapely.intersects(ellipses_col, side_lines)

    # 2. Calculate the "no intersection" distances for all pairs
    # This is the default distance if there's no intersection.
    # The result is a float matrix of shape (num_ellipses, num_sides)
    positive_distances = shapely.distance(ellipses_col, side_lines)

    # 3. Calculate the "intersection" distances (penetration depth)
    # This is done for each ellipse, independent of which side it hits.
    # The result is a 1D array of shape (num_ellipses,).
    penetration_distances = find_farthest_outside_points_ng_vectorized(inner_ellipses, outer_polygon)

    # Make the penetration distances negative and reshape to a column vector (N, 1)
    # so it can be broadcast across all sides for the np.where condition.
    negative_distances = -penetration_distances[:, np.newaxis]

    # 4. Combine the results using the intersection mask
    # np.where(condition, value_if_true, value_if_false)
    # If intersects_mask[i, j] is True, use the negative_distances[i].
    # Otherwise, use the positive_distances[i, j].
    final_distances = np.where(intersects_mask, negative_distances, positive_distances)

    return final_distances


def get_ellipse_distances_ng(inner_ellipse_coords: np.ndarray, inner_ellipse: shapely.geometry.LineString, sides_data):
    """
    Find new center position and rotation for an ellipse based on a new movement parameter.

    This function performs the following steps:
    1. Calculates pivot vector from outer center to inner pivot point
    2. Gets new coordinates and normal vector on the ellipse at new_move position
    3. Adjusts coordinates relative to inner pivot point
    4. Calculates angle difference between old and new pivot vectors
    5. Applies translation and rotation transformations
    6. Calculates distances to side geometries

    Args:
        ellipse (GeometryCollection): The ellipse geometry to transform
        ellipse_data (dict): Dictionary containing ellipse metrics and pivot information
        sides (MultiLineString): Side geometries to measure distances to
        new_move (float): New position parameter (0.0 to 1.0) along the ellipse

    Returns:
        tuple: (distances, angle_diff) where:
            - distances is list of distances from transformed ellipse to sides
            - angle_diff is rotation angle in radians
    """
    distances = np.zeros(2)
    for i, side_line in enumerate(sides_data['lines']):
        if inner_ellipse.intersects(side_line):
            ## ellipse part as a result of the intersection
            dist = find_farthest_outside_point_ng(inner_ellipse_coords, sides_data['polygon'])
            distances[i] = -dist # negative, because of intersection
        else:
            distances[i] = side_line.distance(inner_ellipse)

    return distances



def find_ellipse_position_ng(inner_ellipse_data, outer_ellipse_data, sides_data, front_data, search_scale, tolerance=1e-4):
    """
    Finds the optimal scale factor where the inner and outer distances are equal
    using the efficient Brent's method.
    """
    def objective_function(position):
        # Get the vector for the *target* pivot point, relative to the ellipse's center (which is 0,0)
        new_anchor, new_rotation = get_point_and_rotation_on_ellipse(
            outer_ellipse_data = outer_ellipse_data,
            fraction=position % 1
            )

        ellipse_geometry, _ = create_ellipse(
            anchor_point=new_anchor,
            semi_major_axis=inner_ellipse_data['major'],
            semi_minor_axis=inner_ellipse_data['minor'],
            rotation=new_rotation,
            anchor_on_perimeter=True
            )

        # Calculate distances to sides
        ellipse = shapely.geometry.LineString(ellipse_geometry)

        # Calculate the distances for the given position
        distances = get_ellipse_distances_ng(ellipse_geometry, ellipse, sides_data)
        return distances[0] - distances[1]

    front_data['fractions'][0] *= search_scale
    front_data['fractions'][1] *= search_scale

    return scipy.optimize.brentq(objective_function, a=front_data['fractions'][0], b=front_data['fractions'][1], xtol=tolerance, full_output=True)


def intersection_span_along_line(outer_data, inner_data, sides_data, idx=0):
        """
        Calculates the linear extent (span) of the intersection between a reference line
        and another geometry. It finds the two intersection components that are
        extremal along the reference line and returns the distance between them.
        """
        distances = []
        front_fractions = None
        inner_correction = 0.15 # in mm

        outer_ellipse_geometry, _ = create_ellipse(
            anchor_point=outer_data['center'],
            semi_major_axis=outer_data['major'],
            semi_minor_axis=outer_data['minor'],
            rotation=outer_data['rotation']
            )

        outer_linear_ring = shapely.geometry.LinearRing(outer_ellipse_geometry)
        inter_outer = outer_linear_ring.intersection(sides_data['ring'])

        if inter_outer.is_empty or shapely.get_num_geometries(inter_outer) < 2:
            print('intersection is empty or has less than 2 components (outer) -> intersection_span_along_line()')
            return None

        outer_projected_distances = [outer_linear_ring.project(d, normalized=True) for d in inter_outer.geoms]
        distances.append(
            inter_outer.geoms[np.argmin(outer_projected_distances)].distance(inter_outer.geoms[np.argmax(outer_projected_distances)])
        )

        inner_ellipse_geometry, _ = create_ellipse(
            anchor_point=inner_data['center'],
            semi_major_axis=inner_data['major'] + inner_correction,
            semi_minor_axis=inner_data['minor'] + inner_correction,
            rotation=inner_data['rotation']
            )

        inner_ellipse = shapely.geometry.LinearRing(inner_ellipse_geometry)

        # left half of the ellipse, ccw
        # anchor -----ccw---->end
        left_inner_linear_ring = shapely.geometry.LineString(inner_ellipse_geometry[:36])
        left_point = shapely.ops.nearest_points(left_inner_linear_ring, sides_data['lines'][0])[1]

        # reversed right half of the ellipse, cw
        # anchor -----cw---->end
        right_inner_linear_ring = shapely.geometry.LineString(inner_ellipse_geometry[35:][::-1])
        right_point = shapely.ops.nearest_points(right_inner_linear_ring, sides_data['lines'][1])[1]

        distances.append(left_point.distance(right_point))

        front_fractions = [
            inner_ellipse.project(left_point, normalized=True),
            inner_ellipse.project(right_point, normalized=True)
            ]


        return distances, front_fractions


def solve_quadratic(a, b, c):
    """Solves the quadratic equation At^2 + Bt + C = 0 for t."""
    discriminant = b**2 - 4*a*c
    roots = []
    if discriminant >= 0:
        if a == 0: # Linear equation
            if b != 0:
                roots.append(-c / b)
        else:
            sqrt_discriminant = np.sqrt(discriminant)
            t1 = (-b + sqrt_discriminant) / (2*a)
            t2 = (-b - sqrt_discriminant) / (2*a)
            roots.append(t1)
            # Avoid adding the same root twice if discriminant is close to zero
            if abs(t1 - t2) > 1e-9:
                roots.append(t2)
    return roots


def intersect_line_ellipse(line_p1, line_p2, ellipse_data):
    """
    Finds the intersection points between a line segment and an axis-aligned ellipse.

    Args:
        line_p1 (np.ndarray): First point of the line segment (x1, y1).
        line_p2 (np.ndarray): Second point of the line segment (x2, y2).
        ellipse_center (np.ndarray): Center of the ellipse (h, k).
        semi_major_a (float): Semi-major axis length (along x).
        semi_minor_b (float): Semi-minor axis length (along y).

    Returns:
        list: A list of intersection points (np.ndarray), or an empty list if no intersection.
    """

    ellipse_center = ellipse_data['center']
    b = ellipse_data['major']
    a = ellipse_data['minor']

    x1, y1 = line_p1
    x2, y2 = line_p2
    h, k = ellipse_center

    # Line parameters: x(t) = x1 + t*dx, y(t) = y1 + t*dy
    dx = x2 - x1
    dy = y2 - y1

    # Vector from ellipse center to line start point
    x0 = x1 - h
    y0 = y1 - k

    # Coefficients for the quadratic equation At^2 + Bt + C = 0
    # From: b^2(x0 + t*dx)^2 + a^2(y0 + t*dy)^2 = a^2*b^2
    A = b**2 * dx**2 + a**2 * dy**2
    B = 2 * (b**2 * x0 * dx + a**2 * y0 * dy)
    C = b**2 * x0**2 + a**2 * y0**2 - a**2 * b**2

    # Solve for t
    t_values = solve_quadratic(A, B, C)

    for t in t_values:
        # Check if the intersection point lies within the line segment (0 <= t <= 1)
        if 0.0 <= t <= 1.0:
            ix = x1 + t * dx
            iy = y1 + t * dy
            return np.array([ix, iy])

    return None


def get_ellipse_end_point(polygon_boundary, medial_axis_edge):
        center = shapely.get_coordinates(medial_axis_edge)[-1]
        center_point = shapely.geometry.Point(center)
        outer_semi_axis_length = center_point.distance(polygon_boundary)

        ellipse_geometry, _ = create_ellipse(
            anchor_point=center,
            semi_major_axis=outer_semi_axis_length,
            semi_minor_axis=outer_semi_axis_length,
            rotation=0.0,
            anchor_on_perimeter=False
            )

        ellipse = shapely.geometry.LinearRing(ellipse_geometry)
        if ellipse.intersects(medial_axis_edge):
            inter = ellipse.intersection(medial_axis_edge)
            if inter.geom_type == "MultiPoint":
                distances = [point.project(medial_axis_edge) for point in shapely.get_parts(inter)]
                anchor = shapely.get_coordinates(
                    inter.geoms[np.argmax(distances)])[0]
            elif inter.geom_type == "Point":
                anchor = shapely.get_coordinates(inter)[0]
        else:
            print('ellipse does not intersect medial axis')
            return None

        # rotate 180 degrees (pi radians) around center
        return 2 * center - anchor


def arc_polyline(start, end, center, resolution=71):
    """
    Return a 2-D polyline approximating the circular arc from `start` to `end`
    around `center`.  Always counter-clockwise along the shorter arc.
    """
    start, end, center = map(np.asarray, (start, end, center))

    v_start = start - center
    v_end   = end   - center
    #print lenght of v_start and v_end
    # print(f'v_start: {np.linalg.norm(v_start)}')
    # print(f'v_end: {np.linalg.norm(v_end)}')

    r = np.hypot(*v_start)
    if abs(np.hypot(*v_end) - r) > 1e-1:
        print(f'start end distance: {abs(np.hypot(*v_end) - r)}')
        raise ValueError("start and end must be equidistant from center")

    angle = np.arctan2(v_end[1], v_end[0]) - np.arctan2(v_start[1], v_start[0])
    num_pts = int(abs(angle) / (2 * np.pi) * resolution) + 1
    if num_pts < 3:
        num_pts = 3

    if angle < -np.pi:
        angle += 2 * np.pi
    elif angle > np.pi:
        angle -= 2 * np.pi

    t = np.linspace(0, angle, num_pts)
    cos_t, sin_t = np.cos(t), np.sin(t)

    x = center[0] + v_start[0] * cos_t - v_start[1] * sin_t
    y = center[1] + v_start[0] * sin_t + v_start[1] * cos_t

    points = np.column_stack((x, y))
    # Replace last point with exact end coordinates
    points[-1] = end

    return points


def get_ellipse_data(polygon_boundary, medial_axis_edge, cutter_data, medial_axis_start=True):
        if medial_axis_start:
            idx = 0
        else:
            idx = -1
        center = shapely.get_coordinates(medial_axis_edge)[idx]
        center_point = shapely.geometry.Point(center)
        outer_semi_axis_length = center_point.distance(polygon_boundary)
        inner_axis_length = outer_semi_axis_length - cutter_data['radius']

        outer_ellipse_data = {
            'major': outer_semi_axis_length,
            'minor': outer_semi_axis_length,
            'center': center,
            'perimeter': 2 * np.pi * outer_semi_axis_length
        }

        ellipse_geometry, _ = create_ellipse(
            anchor_point=center,
            semi_major_axis=outer_ellipse_data['major'],
            semi_minor_axis=outer_ellipse_data['minor'],
            rotation=0.0,
            anchor_on_perimeter=False
            )

        ellipse = shapely.geometry.LinearRing(ellipse_geometry)
        if ellipse.intersects(medial_axis_edge):
            inter = ellipse.intersection(medial_axis_edge)
            if inter.geom_type == "MultiPoint":
                distances = [point.project(medial_axis_edge) for point in shapely.get_parts(inter)]
                if medial_axis_start:
                    idx = np.argmin(distances)
                    bounds_center = np.min(distances)/2
                else:
                    idx = np.argmax(distances)
                    bounds_center = 1-(1 - np.max(distances)/2)
                anchor = shapely.get_coordinates(inter.geoms[idx])[0]
            elif inter.geom_type == "Point":
                anchor = shapely.get_coordinates(inter)[0]
                distance = medial_axis_edge.project(inter)

                if medial_axis_start:
                    bounds_center = distance/2
                else:
                    bounds_center = 1-(1 - distance/2)

            outer_ellipse_data['anchor'] = anchor
            outer_ellipse_data['bounds_center'] = shapely.get_coordinates(medial_axis_edge.interpolate(bounds_center))[0]
        else:
            print('ellipse does not intersect medial axis')
            return None, None

        ### Initial rotation of the ellipse in radians ###
        rotation_vector = anchor - center
        outer_ellipse_data['rotation'] = np.arctan2(rotation_vector[1], rotation_vector[0])
        inner_ellipse_data = {**outer_ellipse_data, 'major': inner_axis_length, 'minor': inner_axis_length, 'perimeter': None}

        ## Calc bounds for the optimization
        bounds_coords_margin = (inner_axis_length/2) * 0.5 ##TO-DO: do something with that magic number
        bounds_rotation_margin = 0.2 ##TO-DO: do something with that also
        outer_ellipse_data['bounds_region'] = (outer_ellipse_data['bounds_center'] - bounds_coords_margin, outer_ellipse_data['bounds_center'] + bounds_coords_margin)
        outer_ellipse_data['bounds_rotation'] = (
            ((outer_ellipse_data['rotation']-np.pi/2) - bounds_rotation_margin) % (2 * np.pi),
            ((outer_ellipse_data['rotation']-np.pi/2) + bounds_rotation_margin) % (2 * np.pi)
            )

        return inner_ellipse_data, outer_ellipse_data


def get_ordered_selection() -> list[bpy.types.Object]:
    """Get the selected objects ordered with the active object first."""
    active_obj = bpy.context.active_object
    selected_objects = bpy.context.selected_objects

    if active_obj in selected_objects:
        selected_objects.remove(active_obj)
        selected_objects.insert(0, active_obj)
    return selected_objects if active_obj else []


def get_geometry(apply_transforms: bool = False) -> list[np.ndarray]:
    """Get the geometry of the selected mesh objects."""
    selected_objects = get_ordered_selection()
    if not selected_objects or selected_objects[0].type != 'MESH':
        print("Please select a valid mesh object.")
        return []

    geometry_list = []
    for obj in selected_objects:
        if apply_transforms:
            # Apply all transforms (Location, Rotation, Scale)
            matrix = obj.matrix_world.copy()
        data = obj.data
        vertices = np.empty(len(data.vertices) * 3, dtype=np.float64)
        data.vertices.foreach_get('co', vertices)
        if apply_transforms:
            vertices = np.array([matrix @ Vector((x, y, 0)) for x, y in vertices.reshape((-1, 3))[:, :2]])
        geometry_list.append(vertices.reshape((-1, 3))[:, :2])  # Keep only x, y
    return geometry_list


def boundary_distance(polygon, points):
    """Calculate distances from points to polygon boundary."""
    boundary = polygon.boundary
    return np.array([boundary.distance(shapely.geometry.Point(p)) for p in points])


def advancing_front2(path, polygon, step):
    """
    Find the distances along a path that result in an set of circles
    that are inscribed to a specified polygon and that have an
    advancing front spaced with a specified step apart.

    Arguments
    -----------
    path : (n, 2) float
      2D path inside a polygon
    polygon : shapely.geometry.Polygon
      Object which contains all of path
    step : float
      How far apart should the advancing fronts of the circles be

    Returns
    -----------
    offsets : (m, 2) numpy.ndarray
      2D coordinates of circle centers
    radii : (m,) numpy.ndarray
      Radii of the circles at each position
    """
    path = np.asanyarray(path)
    assert trimesh.util.is_shape(path, (-1, 2))
    assert isinstance(polygon, shapely.geometry.Polygon)

    sampler = trimesh.path.traversal.PathSample(path)
    path_step = step

    distance_initial = np.arange(
        0.0, sampler.length, path_step)

    offsets = sampler.sample(distance_initial)
    radii = boundary_distance(polygon=polygon, points=offsets)

    return offsets, radii


def advancing_front(path, polygon, step):
    """
    Find the distances along a path that result in an set of circles
    that are inscribed to a specified polygon and that have an
    advancing front spaced with a specified step apart.

    Arguments
    -----------
    path : (n, 2) float
      2D path inside a polygon
    polygon : shapely.geometry.Polygon
      Object which contains all of path
    step : float
      How far apart should the advancing fronts of the circles be

    Returns
    -----------
    offsets : (m, 2) numpy.ndarray
      2D coordinates of circle centers
    radii : (m,) numpy.ndarray
      Radii of the circles at each position
    """
    path = np.asanyarray(path)
    assert trimesh.util.is_shape(path, (-1, 2))
    assert isinstance(polygon, shapely.geometry.Polygon)

    sampler = trimesh.path.traversal.PathSample(path)
    path_step = step / 10.0

    distance_initial = np.arange(
        0.0, sampler.length + (path_step / 2.0), path_step)

    offset = sampler.sample(distance_initial)
    radius = boundary_distance(polygon=polygon, points=offset)

    pairs = [(offset[0], radius[0])]
    distance_result = [0]

    offsets = [offset[0]]
    radii = [radius[0]]

    for point, r, pd in zip(offset[1:],
                            radius[1:],
                            distance_initial[1:]):
        vector = point - pairs[-1][0]
        front_distance = np.linalg.norm(vector) - pairs[-1][1] + r
        if front_distance >= step:
            pairs.append((point, r))
            distance_result.append(pd)
            offsets.append(point)
            radii.append(r)

    return np.array(offsets), np.array(radii)


def create_circle_points(center, radius, start_angle, resolution=71, semi_minor=None):
    """Generate points along a circle or ellipse with given center and radius.

    Args:
        center: Center point coordinates (x, y)
        radius: Circle radius or semi-major axis for ellipse
        start_angle: Inital rotation of the circle/ellipse in radians
        resolution: Number of points to sample on the circle/ellipse
        semi_minor: Optional semi-minor axis for ellipse. If None, creates a circle
    """
    theta = np.linspace(start_angle, start_angle + 2*np.pi, resolution)

    # If semi_minor is provided, create an ellipse, otherwise create a circle
    if semi_minor is not None:
        return np.column_stack([
            center[0] + radius * np.cos(theta),
            center[1] + semi_minor * np.sin(theta)
        ])
    else:
        return np.column_stack([
            center[0] + radius * np.cos(theta),
            center[1] + radius * np.sin(theta)
        ])


def create_line_object(coords: np.ndarray, name: str, color=(0, 0, 0, 1)) -> bpy.types.Object:
    """Create a line object in Blender from coordinates.

    Args:
        coords: 2D NumPy array of shape (n, 2) where each row is [x, y]
        name: Name for the created object

    Returns:
        The created Blender object
    """
    mesh = bpy.data.meshes.new(name)
    obj = bpy.data.objects.new(name, mesh)
    bpy.context.collection.objects.link(obj)
    obj.color = color

    # Handle NumPy array format
    vertices = [Vector((row[0], row[1], 0)) for row in coords]
    n = len(vertices)
    edges = [(i, i + 1) for i in range(n - 1)]

    mesh.from_pydata([v.to_tuple() for v in vertices], edges, [])
    mesh.update()

    return obj


def get_side_of_medial_axis(target_ls, reference_ls):
    """
    Determines on which side of a reference LineString a target LineString lies.

    Args:
        target_ls (LineString): The linestring to check.
        reference_ls (LineString): The linestring to use as the reference.

    Returns:
        int: 1 if target_ls is on one side, -1 if on the other.
             Returns 0 if the target_ls is collinear or if reference_ls
             doesn't provide enough information (e.g., is a single point).
    """
    if not isinstance(target_ls, shapely.geometry.LineString) or not isinstance(reference_ls, shapely.geometry.LineString):
        raise TypeError("Both inputs must be Shapely LineString objects.")

    if len(reference_ls.coords) < 2:
        print("Warning: Reference linestring has less than 2 points. Cannot determine side.")
        return 0

    # Get the start point of the reference linestring
    p1 = shapely.get_coordinates(reference_ls.interpolate(0.45, normalized=True)).reshape((-1))

    # Get the end point of the reference linestring
    p2 = shapely.get_coordinates(reference_ls.interpolate(0.55, normalized=True)).reshape((-1))

    # Use the first point of the target linestring as its representative point
    q = shapely.get_coordinates(target_ls.interpolate(0.5, normalized=True)).reshape((-1))

    # Calculate the orientation (2D cross product concept)
    # (p2.x - p1.x) * (q.y - p1.y) - (p2.y - p1.y) * (q.x - p1.x)
    orientation = (p2[0] - p1[0]) * (q[1] - p1[1]) - (p2[1] - p1[1]) * (q[0] - p1[0])

    # Normalize the result to -1, 0, or 1
    if orientation > 0:
        return 1  # One side
    elif orientation < 0:
        return -1 # The other side
    else:
        return 0  # Collinear


def fit_ellipse(
    inner_ellipse_data,
    outer_ellipse_data,
    sides_data,
    cutter_data,
    min_radius_of_curvature=15.0,
    fitter=None,    
    idx=0,
):
    """
    Fit an ellipse within given constraints using optimization.

    This version cleans up diagnostics, ensures proper handling of
    intersection results, and returns updated ellipse data.
    """
    # Configuration constants
    MAXIMUM_RIGHT_DIST = 0.3

    '''
    # -----------------------------------------------------------------
    # 1. Get span information for the current line segment.
    #    The function may return ``None`` if the intersection check fails.
    # -----------------------------------------------------------------
    span_result = intersection_span_along_line(
        outer_ellipse_data, inner_ellipse_data, sides_data, idx
    )
    if span_result is None:
        # If we cannot compute the span, return the input data unchanged.
        return inner_ellipse_data, outer_ellipse_data

    # The function returns (distances, fractions).  The fractions are
    # currently not needed, but we unpack them for completeness.
    distances, _ = span_result

    #0- outer, 1- inner
    print(f'distances o:{ distances[0]/2}, i:{ distances[1]/2} ')
    if distances[0] >= distances[1]:
        print('bigger than previous')
        minimum_major = distances[1]
        maximum_major = distances[0]
    else:
        print('smaller than previous')
        minimum_major = distances[0]
        maximum_major = distances[1]

    min_max_ratio = minimum_major / maximum_major

    if min_max_ratio > 0.8: ## TO-DO: make it dynamic / try optimization
        min_max_correction = 0.25 * min_max_ratio
    else:
        min_max_correction = 0.0

    # print(f'min_max_ratio: {min_max_ratio}, min_max_correction: {min_max_correction}')

    minimum_major /= 2 + min_max_correction
    maximum_major /= 2 - min_max_correction

    '''
    outer_ellipse_geometry, _ = create_ellipse(
        anchor_point=outer_ellipse_data['center'],
        semi_major_axis=outer_ellipse_data['major'],
        semi_minor_axis=outer_ellipse_data['minor'],
        rotation=outer_ellipse_data['rotation']
        )

    outer_ellipse = shapely.geometry.LinearRing(outer_ellipse_geometry)
    shapely.prepare(outer_ellipse)

    inter_outer = outer_ellipse.intersection(sides_data['ring'])

    if inter_outer.is_empty or shapely.get_num_geometries(inter_outer) < 2:
        print('intersection is empty or has less than 2 components (outer) -> intersection_span_along_line()')
        return None

    outer_projected_distances = [outer_ellipse.project(d, normalized=True) for d in inter_outer.geoms]

    sides_fractions = [
        sides_data['lines'][0].project(inter_outer.geoms[np.argmin(outer_projected_distances)], normalized=True),
        sides_data['lines'][1].project(inter_outer.geoms[np.argmax(outer_projected_distances)], normalized=True)
    ]

    # print(f'sides_fractions: {sides_fractions}')

    front_fractions_pop = [min(outer_projected_distances), -(1-max(outer_projected_distances))] #front fractions range: -/0-1
    front_line_part0 = shapely.ops.substring(outer_ellipse, 0, min(outer_projected_distances), normalized=True)
    front_line_part1 = shapely.ops.substring(outer_ellipse, max(outer_projected_distances), 1, normalized=True)
    front_line = shapely.ops.linemerge([front_line_part0, front_line_part1])
    front_line_front = front_line.offset_curve(-cutter_data['radius'], join_style=2, mitre_limit=2.0).simplify(1)
    front_line_back = front_line.offset_curve(-MAXIMUM_RIGHT_DIST, join_style=2, mitre_limit=2.0)
    front_poly = shapely.geometry.Polygon(list(front_line_front.coords) + list(reversed(front_line_back.coords)))
    buffered_front_line = front_line.buffer(MAXIMUM_RIGHT_DIST, join_style=2, mitre_limit=2.0, cap_style='flat')

    shapely.prepare(buffered_front_line)
    shapely.prepare(front_poly)

    front_data = {
        'line': front_line,
        'outer_polygon': front_poly,
        'buffered_line': buffered_front_line,
        'fractions': front_fractions_pop,
        'sides_fractions': sides_fractions,
        'length': front_line.length
    }

    test_inner_data = inner_ellipse_data.copy()
    test_inner_data['anchor'] = outer_ellipse_data['anchor']

    ## works only on the first iteration. TO-DO: move from loop.
    ellipse_geometry, _ = create_ellipse(
        anchor_point=inner_ellipse_data['center'],
        semi_major_axis=inner_ellipse_data['major']+0.25,
        semi_minor_axis=inner_ellipse_data['minor']+0.25,
        rotation=inner_ellipse_data['rotation']
    )
    ellipse = shapely.geometry.LinearRing(ellipse_geometry)

    sides_fractions_inner = []
    for side in sides_data['lines']:
        inter = ellipse.intersection(side)
        if inter.geom_type == "Point":
            sides_fractions_inner.append(side.project(inter, normalized=True))
        else:
            print('ellipse intersection with side is not a point')

    sides_parts = []
    for inner, outer, side in zip(
            sides_fractions_inner,
            front_data['sides_fractions'],
            sides_data['lines']
        ):
        sides_parts.append(shapely.ops.substring(side, inner, outer, normalized=True))

    distances_ellipse = [0.5, 0.75, 0.15, 0.1, 0.2] #A, Br, Bl, Cr, Cl
    point_A = shapely.get_coordinates(front_data['line'].interpolate(distances_ellipse[0], normalized=True))
    point_Br = shapely.get_coordinates(front_data['line'].interpolate(distances_ellipse[1], normalized=True))
    point_Bl = shapely.get_coordinates(front_data['line'].interpolate(distances_ellipse[2], normalized=True))
    point_Cr = shapely.get_coordinates(sides_parts[1].interpolate(distances_ellipse[3], normalized=True))
    point_Cl = shapely.get_coordinates(sides_parts[0].interpolate(distances_ellipse[4], normalized=True))
    points = np.vstack([point_A, point_Br, point_Bl, point_Cr, point_Cl], dtype=np.float64)
    
    def _objective_sides(position, idx):
        #idx = 0 -> left, idx = 1 -> right
        point = shapely.get_coordinates(sides_parts[idx].interpolate(position, normalized=True))
        points[-1-idx] = point
        result = fitter.fit(points[:, 0], points[:, 1], method='svd')
        
        ellipse_geometry, _ = create_ellipse(
            anchor_point=(result['center'][0], result['center'][1]),
            semi_major_axis=result['semi_major'],
            semi_minor_axis=result['semi_minor'],
            rotation=result['rotation']+np.pi/2
        )        
        ellipse = shapely.geometry.LinearRing(ellipse_geometry)

        if ellipse.intersects(sides_data['lines'][idx]): #right
            inter = ellipse.intersection(sides_data['lines'][idx])
            if inter.geom_type == "MultiPoint":
                pts = shapely.get_parts(inter)
                # For Side
                pA, pB = sorted(pts, key=lambda p: sides_data['lines'][idx].project(p))
                side_part = shapely.ops.substring(sides_data['lines'][idx], sides_data['lines'][idx].project(pA), sides_data['lines'][idx].project(pB))
                # For ellipse
                qA, qB = sorted(pts, key=lambda p: ellipse.project(p))
                ellipse_part = shapely.ops.substring(ellipse, ellipse.project(qA), ellipse.project(qB))
                max_distance = ellipse_part.hausdorff_distance(side_part)
        else:
            max_distance = ellipse.distance(sides_data['lines'][idx])
        
        return max_distance
    
    def _objective_front(position, idx):
        #idx = 0 -> left, idx = 1 -> right
        point = shapely.get_coordinates(front_data['line'].interpolate(position, normalized=True))
        points[2-idx] = point
        result = fitter.fit(points[:, 0], points[:, 1], method='svd')

        r
        
        ellipse_geometry, _ = create_ellipse(
            anchor_point=(result['center'][0], result['center'][1]),
            semi_major_axis=result['semi_major'],
            semi_minor_axis=result['semi_minor'],
            rotation=result['rotation']+np.pi/2
        )
        if idx == 0: #left half
            ellipse_half = ellipse_geometry[int(len(ellipse_geometry)/2):]
        else: #right half
            ellipse_half = ellipse_geometry[:int(len(ellipse_geometry)/2)]
        max_distance = find_farthest_outside_point_ng(ellipse_half, shapely.geometry.Polygon(outer_ellipse_geometry))
        print(f'idx: {idx}, position: {position}, max_distance: {max_distance}')
        
        return max_distance

    side_points_coords = []
    for idx in range(2):
        min_Cx = scipy.optimize.minimize_scalar(_objective_sides, bounds=(0, 1), method='bounded', options={'xatol': 1e-3}, args=(idx,))
        point_Cx = shapely.get_coordinates(sides_parts[idx].interpolate(min_Cx.x, normalized=True))
        side_points_coords.append(point_Cx)

    front_points_coords = []
    for idx in range(2):
        if idx == 0: #left
            bounds = (0.1, 0.4)
        else: #right
            bounds = (0.6, 0.9)
        min_Bx = scipy.optimize.minimize_scalar(_objective_front, bounds=bounds, method='bounded', options={'xatol': 1e-3}, args=(idx,))
        print(f'idx: {idx}, min_Bx: {min_Bx}')
        point_Bx = shapely.get_coordinates(front_data['line'].interpolate(min_Bx.x, normalized=True))        
        front_points_coords.append(point_Bx)

    create_line_object(point_A,  "point_A", color=(1, 0, 0, 1))
    create_line_object(side_points_coords[0],  "point_Cl", color=(1, 0, 0, 1))    
    create_line_object(side_points_coords[1],  "point_Cr", color=(1, 0, 0, 1))
    create_line_object(front_points_coords[0],  "point_Bl", color=(1, 0, 0, 1))    
    create_line_object(front_points_coords[1],  "point_Br", color=(1, 0, 0, 1))

    points = np.vstack([point_A, front_points_coords[0], front_points_coords[1], side_points_coords[0], side_points_coords[1]], dtype=np.float64)
    result = fitter.fit(points[:, 0], points[:, 1], method='svd')
    
    print(f'rotation: {result["rotation"]}, ellipse rotation: {inner_ellipse_data['rotation']}')    
    
    ellipse_geometry, _ = create_ellipse(
        anchor_point=(result['center'][0], result['center'][1]),
        semi_major_axis=result['semi_major'],
        semi_minor_axis=result['semi_minor'],
        rotation=result['rotation']+np.pi/2
    )
    create_line_object(ellipse_geometry, "ellipse_outer", color=(1, 1, 0, 1))

    # print(f'cost: {result["cost"]}')
    # start_half = ellipse_geometry[:int(len(ellipse_geometry)/2)]
    # end_half = ellipse_geometry[int(len(ellipse_geometry)/2):]
    # start_half_distance = find_farthest_outside_point_ng(start_half, shapely.geometry.Polygon(outer_ellipse_geometry))
    # end_half_distance = find_farthest_outside_point_ng(end_half, shapely.geometry.Polygon(outer_ellipse_geometry))
    # print(f'start_half_distance: {start_half_distance}, end_half_distance: {end_half_distance}')

    # create_line_object(ellipse_geometry, "ellipse_outer", color=(1, 1, 0, 1))
    # print(params)

    return


    time1 = time.time()
    position = find_ellipse_position_ng(
                test_inner_data, outer_ellipse_data, sides_data, front_data, search_scale=search_scale, tolerance=1e-3
            )
    time2 = time.time()
    print(f'Time: {time2-time1}')
    print(f'position: {position}')
    return


    # Create ellipse geometry for boundary checking
    ellipse_geometry, _ = create_ellipse(
        anchor_point=new_anchor,
        semi_major_axis=test_inner_data['major'],
        semi_minor_axis=test_inner_data['minor'],
        rotation=new_rotation,
        anchor_on_perimeter=True
    )

    # ellipse = shapely.geometry.LinearRing(ellipse_geometry)
    create_line_object(ellipse_geometry, "ellipse_inner", color=(1, 0, 0, 1))
    return {},{}

    '''

    ellipse_geometry, ellipse_center = create_ellipse(
        anchor_point=optimization_result.new_anchor,
        semi_major_axis=optimal_major,
        semi_minor_axis=optimization_result.minor,
        rotation=optimization_result.new_rotation,
        anchor_on_perimeter=True
    )
    create_line_object(ellipse_geometry, "ellipse_inner", color=(1, 0, 0, 1))

    # Update inner ellipse data with optimization results
    inner_ellipse_data.update({
        'major': optimal_major,
        'minor': optimization_result.minor,
        'center': ellipse_center,
        'anchor': optimization_result.new_anchor,
        'rotation': optimization_result.new_rotation
    })

    # Calculate outer ellipse parameters
    outer_major = inner_ellipse_data['major'] + cutter_data['radius']
    outer_minor = inner_ellipse_data['minor'] + cutter_data['radius']

    # Calculate ellipse perimeter using exact formula
    perimeter = _calculate_ellipse_perimeter(outer_major, outer_minor)

    # Calculate new anchor position for outer ellipse
    anchor_offset = rotate_vector(
        np.array([cutter_data['radius'], 0]),
        inner_ellipse_data['rotation']
    )

    # Construct final outer ellipse data
    outer_ellipse_result = {
        'major': outer_major,
        'minor': outer_minor,
        'center': ellipse_center,
        'anchor': inner_ellipse_data['anchor'] + anchor_offset,
        'rotation': inner_ellipse_data['rotation'],
        'perimeter': perimeter
    }

    ellipse_geometry, ellipse_center = create_ellipse(
        anchor_point=outer_ellipse_result['anchor'],
        semi_major_axis=outer_ellipse_data['major'],
        semi_minor_axis=outer_ellipse_data['minor'],
        rotation=outer_ellipse_data['rotation'],
        anchor_on_perimeter=True
    )
    # create_line_object(ellipse_geometry, "ellipse_outer", color=(0, 1, 0, 1))

    return inner_ellipse_data, outer_ellipse_result
    '''


def get_split_coords(ellipse_data):
    cx, cy = ellipse_data['center']
    angle_rad = ellipse_data['rotation']+np.pi
    x = cx + ellipse_data['major'] * np.cos(angle_rad)
    y = cy + ellipse_data['major'] * np.sin(angle_rad)
    return np.array([x, y])


def resample_linestring(line: shapely.geometry.LineString, segment_length: float) -> shapely.geometry.LineString:
    """
    Resamples a Shapely LineString to have segments of a constant length.

    The last segment will be shorter than segment_length if the total
    length is not a multiple of segment_length.

    Args:
        line (LineString): The original LineString to resample.
        segment_length (float): The desired length of each segment.

    Returns:
        LineString: The new, resampled LineString.
    """
    if segment_length <= 0:
        raise ValueError("Segment length must be positive.")

    # Get the total length of the original line
    total_length = line.length

    # Generate distances along the line at which to place new vertices
    # np.arange creates a sequence from 0 to total_length with a step of segment_length
    distances = np.arange(0, total_length, segment_length)

    # Interpolate points at these distances
    # The list comprehension is a concise way to do this
    new_points = [line.interpolate(dist) for dist in distances]

    # Always include the very last point of the original line to ensure
    # the resampled line has the same extent.
    # We can access the last coordinate directly.
    last_point = shapely.geometry.Point(line.coords[-1])
    if not new_points[-1].equals(last_point):
         new_points.append(last_point)


    # Create a new LineString from the list of points
    return shapely.geometry.LineString(new_points)


def main():
    cutter_dim = 10 # in mm

    cutter_data = {
        'diameter': cutter_dim,
        'radius': cutter_dim / 2
    }

    # Get geometry and validate
    geometry = get_geometry(apply_transforms=True)

    # l0 = shapely.geometry.LineString(geometry[0])
    # l1 = shapely.geometry.LineString(geometry[1])

    # hausdorff_distance = l0.hausdorff_distance(l1)
    # print(f'Hausdorff distance: {hausdorff_distance}')
    # return

    biggest_gap = None
    biggest_gap_idx = None

    # Check distance between first and last point of each geometry, to find the medial axis.
    for idx, geom in enumerate(geometry[1:]):
        if len(geom) >= 2:  # Ensure geometry has at least 2 points
            start_point = geom[0]
            end_point = geom[-1]
            distance = np.linalg.norm(np.array(end_point) - np.array(start_point))

            if biggest_gap is None or distance > biggest_gap:
                biggest_gap = distance
                biggest_gap_idx = idx+1 # +1 because we skip the first geometry

    #reorder geometry so that the medial axis is the second element
    if biggest_gap_idx is not None:
        geometry = [geometry[0]] + [geometry[biggest_gap_idx]] + geometry[1:biggest_gap_idx] + geometry[biggest_gap_idx+1:]

    if len(geometry) < 3:
        print('Please select at least three objects.')
    else:
        holes = [geom for geom in geometry[2:]]
        polygon = shapely.geometry.Polygon(shell=geometry[0], holes=holes).normalize() ## reorder rings if they are not in the expected orientation (exterior ccw, interiors cw)
        medial_axis = shapely.geometry.LineString(geometry[1])

    polygon_buffered = polygon.buffer(-cutter_data['radius'])
    medial_middle = medial_axis.interpolate(0.5, normalized=True)
    boundary = polygon.boundary
    buffered_boundary = polygon_buffered.boundary


    all_geoms = [shapely.geometry.LineString(polygon_buffered.exterior.coords)] + \
                 [shapely.geometry.LineString(interior.coords) for interior in polygon_buffered.interiors]

    tolerance = 0.03
    distances = []
    for line in all_geoms:
        distance = medial_middle.distance(line)
        distances.append(distance)

    active_geoms = [line for line, distance in zip(all_geoms, distances) if abs(distance - min(distances)) < tolerance]

    # for geom in active_geoms:
    #     create_line_object(geom.coords, 'active_geom', color=(1, 0, 0, 1))


    ## Sides per path
    trochos_offsets, trochos_radii = advancing_front(geometry[1], polygon_buffered, 1) # 5mm between trochoids
    # trochos_radii[0] += 0.5 #quick fix for intersection_span_along_line(), we need bigger first circle for proper intersection check.
    # print(trochos_offsets, trochos_radii)
    # return

    ##metrics of the medial axis ends
    ma_start_coords = shapely.get_coordinates(medial_axis)[0]
    ma_start_point = shapely.geometry.Point(medial_axis.coords[0])
    ma_start_radius = ma_start_point.distance(buffered_boundary)

    ma_end_coords = shapely.get_coordinates(medial_axis)[-1]
    ma_end_point = shapely.geometry.Point(medial_axis.coords[-1])
    ma_end_radius = ma_end_point.distance(buffered_boundary)

    points_array = shapely.points(trochos_offsets)
    buffered_points_array = shapely.buffer(points_array, trochos_radii, quad_segs=18) #about 72 points per circle
    buffered_points_array[0] = shapely.buffer(points_array[0], trochos_radii[0], quad_segs=36)
    buffered_points_array[-1] = shapely.buffer(points_array[-1], trochos_radii[-1], quad_segs=36)

    trochos_poly = shapely.unary_union(buffered_points_array).simplify(0.05)
    shapely.prepare(trochos_poly)
    trochos_poly_coords = shapely.get_coordinates(trochos_poly.exterior)

    ##ellipse data
    inner_ellipse_data, outer_ellipse_data = get_ellipse_data(boundary, medial_axis, cutter_data, medial_axis_start=True)
    end_inner_ellipse_data, _ = get_ellipse_data(boundary, medial_axis, cutter_data, medial_axis_start=False)

    start_split_coords = get_split_coords(inner_ellipse_data)
    end_split_coords = get_split_coords(end_inner_ellipse_data)
    distances =scipy.spatial.distance.cdist([start_split_coords, end_split_coords], trochos_poly_coords)  # Shape: (len(P), len(points))
    split0, split1 = np.argmin(distances, axis=1)

    # Ensure proper ordering
    start, end = min(split0, split1), max(split0, split1)

    # Create two continuous segments
    side0 = shapely.geometry.LineString(trochos_poly_coords[start:end+1])
    side1_coords = np.concatenate((trochos_poly_coords[end:], trochos_poly_coords[:start+1]))
    side1 = shapely.geometry.LineString(side1_coords)
    sides = [side0, side1]
    # sides = [resample_linestring(side, 0.1) for side in sides]

    ### Determine sides, the first side is the one that is on the "left" of the medial axis
    if get_side_of_medial_axis(sides[0], medial_axis) == -1:
        sides.reverse()

    # reverse right side for match direction with left: left -->, right -->
    sides[1] = sides[1].reverse()

    for side in sides:
        shapely.prepare(side)

    create_line_object(sides[0].coords, "side_left", color=(0, 1, 0, 1))
    create_line_object(sides[1].coords, "side_right", color=(1, 0, 0, 1))

    trochos_ring = shapely.geometry.LinearRing(trochos_poly.exterior.coords)
    shapely.prepare(trochos_ring)

    sides_data = {
        'ring': trochos_ring,
        'polygon': trochos_poly,
        'lines': sides
    }

    ellipse_geometry, _ = create_ellipse(
        anchor_point=inner_ellipse_data['center'],
        semi_major_axis=inner_ellipse_data['major'],
        semi_minor_axis=inner_ellipse_data['minor'],
        rotation=inner_ellipse_data['rotation']
        )
    ellipse_geometry, _ = create_ellipse(
        anchor_point=outer_ellipse_data['center'],
        semi_major_axis=outer_ellipse_data['major'],
        semi_minor_axis=outer_ellipse_data['minor'],
        rotation=outer_ellipse_data['rotation']
        )

    create_line_object(ellipse_geometry, "ellipse_inner", color=(1, 0, 0, 1))
    create_line_object(ellipse_geometry, "ellipse_outer", color=(0, 1, 0, 1))

    min_radius_of_curvature = 10.0    
    fitter = EllipseFitter()
    for i in range(1):
        print(f'iteration: {i}')
        inner_ellipse_data, outer_ellipse_data = fit_ellipse(
            inner_ellipse_data,
            outer_ellipse_data,
            sides_data,
            cutter_data,
            min_radius_of_curvature,
            fitter,            
            i
            )

if __name__ == "__main__":
    main()
